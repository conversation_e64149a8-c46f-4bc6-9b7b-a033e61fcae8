import React, { useState, useCallback } from "react";
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  ScrollView,
  Image,
  TouchableOpacity,
  FlatList,
  Dimensions,
  RefreshControl,
} from "react-native";
import { Link } from "expo-router";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { LinearGradient } from "expo-linear-gradient";
import type { Group } from "@/lib/api/types";
import { useGroups } from "@/lib/api/queries";

interface Topic {
  id: string;
  title: string;
  image: string;
}

const ExploreScreen: React.FC = () => {
  const { isPending, error, data, refetch } = useGroups();
  const [refreshing, setRefreshing] = useState(false);

  const onRefresh = useCallback(async () => {
    console.log("Explore: Pull to refresh started");
    setRefreshing(true);
    try {
      console.log("Explore: Calling refetch for groups data...");
      await refetch();
      console.log("Explore: Refetch completed successfully");
    } catch (error) {
      console.error("Explore: Refetch failed:", error);
    } finally {
      setRefreshing(false);
      console.log("Explore: Pull to refresh finished");
    }
  }, [refetch]);

  let groups: Group[] = data?.data.groups ?? [];

  if (error)
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={48} color="#fff" />
          <Text style={styles.errorText}>
            An error has occurred: {error.message}
          </Text>
        </View>
      </SafeAreaView>
    );

  const renderGroupCard = ({ item }: { item: Group }) => {
    return (
      <Link href={`/groups/${item.externalId}`} asChild>
        <TouchableOpacity style={styles.masterclassCard}>
          <Image
            source={{ uri: item.bannerImage }}
            style={styles.masterclassImage}
            height={200}
          />
          <LinearGradient
            colors={["transparent", "rgba(0,0,0,0.7)", "rgba(0,0,0,0.9)"]}
            style={styles.gradientOverlay}
          >
            <Text style={styles.masterclassTitle}>{item.name}</Text>
            <View style={styles.tagsContainer}>
              {item.tags?.map((tag, index) => (
                <React.Fragment key={index}>
                  <View style={styles.tagContainer}>
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                  {index < item.tags.length - 1 && (
                    <View style={styles.tagDivider}></View>
                  )}
                </React.Fragment>
              ))}
            </View>
            <View style={styles.instructorContainer}>
              <Image
                source={{ uri: item.creatorAvatarURL }}
                style={styles.instructorAvatar}
              />
              <Text style={styles.instructorName}>{item.creatorName}</Text>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </Link>
    );
  };

  const GroupSkeleton = () => (
    <View style={styles.masterclassCard}>
      <View style={styles.masterclassImageSkeleton} />
      <View
        style={[styles.gradientOverlay, { backgroundColor: "rgba(0,0,0,0.3)" }]}
      >
        <View style={styles.titleSkeleton} />
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <Text style={styles.sectionTitle}>Explore</Text>
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#fff"
            colors={["#fff"]}
          />
        }
      >
        {/* <Text style={styles.sectionTitle}>Explore New Topics</Text>

        {isPending ? (
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.topicsContainer}
          >
            {[1, 2, 3].map((item) => (
              <TopicSkeleton key={item} />
            ))}
          </ScrollView>
        ) : (
          <FlatList
            data={topics}
            renderItem={renderTopicCard}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.topicsContainer}
          />
        )} */}

        {isPending ? (
          <View>
            {[1, 2, 3].map((item) => (
              <GroupSkeleton key={item} />
            ))}
          </View>
        ) : (
          groups.map((group) => (
            <View key={group.id}>{renderGroupCard({ item: group })}</View>
          ))
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const { width } = Dimensions.get("window");

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  scrollContainer: {
    padding: 16,
  },
  sectionTitle: {
    paddingLeft: 16,
    paddingTop: 16,
    fontSize: 20,
    fontWeight: "800",
    color: "#fff",
    marginBottom: 24,
  },
  topicsContainer: {
    paddingRight: 16,
  },
  topicCard: {
    width: 140,
    height: 140,
    marginRight: 12,
    borderRadius: 12,
    overflow: "hidden",
  },
  topicImage: {
    width: "100%",
    height: "100%",
  },
  topicTitle: {
    position: "absolute",
    bottom: 12,
    left: 12,
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  masterclassCard: {
    marginBottom: 16,
    borderRadius: 12,
    overflow: "hidden",
    backgroundColor: "#1A1A1A",
    position: "relative",
  },
  masterclassImage: {
    width: "100%",
    height: 200,
  },
  gradientOverlay: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: "100%",
    justifyContent: "flex-end",
    padding: 16,
  },
  masterclassTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 4,
  },
  masterclassInfo: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  masterclassType: {
    fontSize: 14,
    color: "#999",
    marginRight: 8,
  },
  masterclassLessons: {
    fontSize: 14,
    color: "#999",
  },
  instructorContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  instructorAvatar: {
    width: 20,
    height: 20,
    borderRadius: 12,
    marginRight: 8,
    borderWidth: 1,
    borderColor: "#fff",
    backgroundColor: "#2A2A2A", // fallback color if image fails to load
  },
  instructorName: {
    fontSize: 12,
    color: "#fff",
    fontWeight: "500",
  },
  statusBadge: {
    position: "absolute",
    top: 16,
    right: 16,
    backgroundColor: "#E53935",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
  },
  statusText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "500",
  },
  // Skeleton styles
  topicCardSkeleton: {
    width: 140,
    height: 140,
    marginRight: 12,
    borderRadius: 12,
    backgroundColor: "#1A1A1A",
    overflow: "hidden",
  },
  topicImageSkeleton: {
    width: "100%",
    height: "100%",
    backgroundColor: "#2A2A2A",
  },
  masterclassImageSkeleton: {
    width: "100%",
    height: 200,
    backgroundColor: "#2A2A2A",
  },
  titleSkeleton: {
    width: width * 0.7,
    height: 20,
    backgroundColor: "#2A2A2A",
    borderRadius: 4,
    marginBottom: 8,
  },
  infoSkeleton: {
    width: width * 0.4,
    height: 16,
    backgroundColor: "#2A2A2A",
    borderRadius: 4,
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  errorText: {
    color: "#fff",
    fontSize: 16,
    textAlign: "center",
    marginTop: 16,
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: 6,
    marginBottom: 8,
    alignItems: "center",
    opacity: 0.5,
  },
  tagContainer: {},
  tagText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "500",
  },
  tagDivider: {
    backgroundColor: "#fff",
    fontSize: 12,
    height: 4,
    width: 4,
    borderRadius: 2,
  },
});

export default ExploreScreen;
