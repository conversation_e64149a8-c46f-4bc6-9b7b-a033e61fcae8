import React, { useEffect, useState } from "react";
import { Tabs } from "expo-router";
import { View, Text, StyleSheet } from "react-native";
import {
  Ungroup,
  Search,
  Bell,
  MessageCircleMore,
  UserCircle,
} from "lucide-react-native";

import { useColorScheme } from "@/hooks/useColorScheme";
import { useNotifications } from "@/lib/api/queries";

const NotificationIcon: React.FC<{ color: string; focused: boolean }> = ({
  color,
  focused,
}) => {
  const [unreadCount, setUnreadCount] = useState(0);
  const { data } = useNotifications();

  // Calculate unread notifications count
  useEffect(() => {
    if (data?.data?.notifications) {
      const count = data.data.notifications.filter(
        (notification) => !notification.isRead,
      ).length;
      setUnreadCount(count);
    }
  }, [data]);

  return (
    <View style={{ width: 24, height: 24 }}>
      <Bell color={color} size={24} strokeWidth={1.5} />
      {unreadCount > 0 && (
        <View style={badgeStyles.badge}>
          <Text style={badgeStyles.badgeText}>
            {unreadCount > 99 ? "99+" : unreadCount}
          </Text>
        </View>
      )}
    </View>
  );
};

// Badge styles
const badgeStyles = StyleSheet.create({
  badge: {
    position: "absolute",
    right: -6,
    top: -4,
    backgroundColor: "#FF4545",
    borderRadius: 10,
    minWidth: 16,
    height: 16,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 4,
  },
  badgeText: {
    color: "white",
    fontSize: 10,
    fontWeight: "bold",
    textAlign: "center",
  },
});

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === "dark";

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: "#FF4545",
        tabBarInactiveTintColor: "#FFFFFF",
        headerShown: false,
        tabBarStyle: {
          backgroundColor: "rgba(19, 19, 19, 1)",
          borderTopWidth: 0,
          elevation: 0,
          paddingTop: 5,
          // paddingBottom: 20,
        },
        tabBarLabelStyle: {
          fontFamily: "Inter",
        },
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: "Home",
          tabBarIcon: ({ color }) => (
            <Ungroup color={color} size={24} strokeWidth={1.5} />
          ),
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: "Explore",
          tabBarIcon: ({ color }) => (
            <Search color={color} size={24} strokeWidth={1.5} />
          ),
        }}
      />
      <Tabs.Screen
        name="notifications"
        options={{
          title: "Notification",
          tabBarIcon: ({ color, focused }) => (
            <NotificationIcon color={color} focused={focused} />
          ),
        }}
      />
      <Tabs.Screen
        name="chats"
        options={{
          title: "Chats",
          tabBarIcon: ({ color }) => (
            <MessageCircleMore color={color} size={24} strokeWidth={1.5} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: "Profile",
          tabBarIcon: ({ color }) => (
            <UserCircle color={color} size={24} strokeWidth={1.5} />
          ),
        }}
      />
    </Tabs>
  );
}
