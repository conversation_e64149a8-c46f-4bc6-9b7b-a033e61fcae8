import React from "react";
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  FlatList,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from "react-native";
import { Bell } from "lucide-react-native";
import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";

import { useNotifications, useReadNotification } from "@/lib/api/queries";
import { Notification, StreamNotification } from "@/lib/api/types";
import { useAppContext } from "@/context/app";

interface ExtendedNotification extends Notification {
  type: "activity" | "announcement";
}

interface StreamNotificationExtended extends StreamNotification {
  type: "activity" | "announcement";
  title: string;
  body: string;
  isRead: boolean;
  createdAt: string;
  data?: {
    link?: string;
  };
}

type CombinedNotification = ExtendedNotification | StreamNotificationExtended;

type TabType = "activities" | "announcements";

const NotificationItem: React.FC<{ item: CombinedNotification }> = ({
  item,
}) => {
  const { mutate: markAsRead, isPending } = useReadNotification();

  // Format the date and time
  const formatTimeAgo = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    // Less than a minute
    if (diffInSeconds < 60) {
      return "Just now";
    }

    // Less than an hour
    if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} ${minutes === 1 ? "minute" : "minutes"} ago`;
    }

    // Less than a day
    if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} ${hours === 1 ? "hour" : "hours"} ago`;
    }

    // Less than a week
    if (diffInSeconds < 604800) {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} ${days === 1 ? "day" : "days"} ago`;
    }

    // Format as date with time
    return new Date(dateString).toLocaleString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const timeAgo = formatTimeAgo(item.createdAt);

  const handleNotificationPress = () => {
    // Mark notification as read if it's not already read (only for regular notifications)
    if (!item.isRead && "id" in item && typeof item.id === "number") {
      markAsRead({ notificationId: item.id.toString() });
    }

    // Navigate to the link if available
    if (item.data?.link) {
      try {
        // Extract the path from the full URL if it's a deep link
        const url = new URL(item.data.link);

        // If the URL is an expo-router deep link, handle it appropriately
        if (url.hostname === "sphere-app.expo.app") {
          // Extract path components for structured navigation
          const pathSegments = url.pathname.split("/");

          // Remove empty segments and build a structured path object
          const cleanSegments = pathSegments.filter(
            (segment) => segment.length > 0,
          );

          if (cleanSegments.length > 0) {
            // Log the navigation path
            console.log("Navigating to path:", cleanSegments.join("/"));

            // Use the structured path format that Expo Router expects
            const path = `/${cleanSegments.join("/")}` as any;
            router.push({
              pathname: path,
              params: Object.fromEntries(new URLSearchParams(url.search)),
            });
          }
        } else {
          // For external links, we could open them in a browser
          console.log("External link, would open in browser:", item.data.link);
          // Implement external link handling if needed
        }
      } catch (error) {
        console.error("Failed to parse or navigate to link:", error);
        Alert.alert(
          "Navigation Error",
          "Could not open the notification link.",
        );
      }
    }
  };

  return (
    <TouchableOpacity
      style={[
        styles.notificationItem,
        isPending && styles.notificationItemDisabled,
      ]}
      onPress={handleNotificationPress}
      disabled={isPending}
    >
      <View style={styles.notificationHeader}>
        <View style={[styles.avatar, styles.avatarPlaceholder]}>
          <MaterialIcons name="notifications" size={20} color="#fff" />
        </View>
        <View style={styles.notificationContent}>
          <Text style={styles.notificationTitle}>{item.title}</Text>
          <Text style={styles.notificationMessage}>{item.body}</Text>
          <Text style={styles.notificationTime}>{timeAgo}</Text>
        </View>
        {!item.isRead && <View style={styles.unreadIndicator} />}
      </View>
    </TouchableOpacity>
  );
};

const NotificationsScreen: React.FC = () => {
  const [activeTab, setActiveTab] = React.useState<TabType>("activities");
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [streamNotifications, setStreamNotifications] = React.useState<any[]>(
    [],
  );
  const [streamLoading, setStreamLoading] = React.useState(false);
  const { data, isLoading, isError, refetch } = useNotifications();
  const { userId, streamClient } = useAppContext();

  const handleTabPress = (tab: TabType) => {
    setActiveTab(tab);
  };

  const fetchStreamNotifications = React.useCallback(async () => {
    if (!streamClient) {
      setStreamNotifications([]);
      return;
    }

    setStreamLoading(true);
    try {
      // Get notification feed - typically 'notification' feed type
      const notificationFeed = streamClient.feed("notification", userId);

      // Fetch notification activities
      const response = await notificationFeed.get({
        limit: 50,
        withReactionCounts: true,
        withOwnReactions: true,
      });

      setStreamNotifications(response.results || []);
    } catch (error) {
      console.error("Error fetching Stream notifications:", error);
      setStreamNotifications([]);
    } finally {
      setStreamLoading(false);
    }
  }, [streamClient]);

  React.useEffect(() => {
    fetchStreamNotifications();
  }, [fetchStreamNotifications]);

  // Determine notification type based on content or other criteria
  const determineNotificationType = (
    notification: Notification,
  ): "activity" | "announcement" => {
    // This is a placeholder logic - you should implement actual logic based on your requirements
    // For example, you might determine type based on the notification content, link pattern, or other data
    if (notification.data?.link?.includes("live-classes")) {
      return "activity";
    }
    return "announcement";
  };

  // Transform GetStream notifications to match our notification format
  const transformStreamNotification = (
    streamNotification: any,
  ): StreamNotificationExtended => {
    // Create a readable title and body from the stream notification
    const getActivityTitle = (notification: any): string => {
      const verb = notification.verb;
      const actorName =
        typeof notification.actor === "string"
          ? notification.actor
          : notification.actor?.data?.name || "Someone";

      switch (verb) {
        case "post":
          return `${actorName} posted`;
        case "like":
          return `${actorName} liked your post`;
        case "comment":
          return `${actorName} commented on your post`;
        case "follow":
          return `${actorName} started following you`;
        default:
          return `${actorName} ${verb}`;
      }
    };

    const getActivityBody = (notification: any): string => {
      if (typeof notification.object === "string") {
        return notification.object;
      }
      if (notification.object?.message) {
        return notification.object.message;
      }
      if (notification.object?.text) {
        return notification.object.text;
      }
      return `New ${notification.verb} activity`;
    };

    return {
      ...streamNotification,
      type: "announcement", // GetStream notifications go to announcements
      title: getActivityTitle(streamNotification),
      body: getActivityBody(streamNotification),
      isRead: streamNotification.is_read || false,
      createdAt: streamNotification.time,
      data: {
        link: streamNotification.foreign_id || undefined,
      },
    };
  };

  // Filter notifications based on active tab
  const filteredNotifications = React.useMemo(() => {
    console.log("data", data);
    console.log("streamNotifications", streamNotifications);

    // Regular notifications
    const regularNotifications: ExtendedNotification[] =
      data?.data?.notifications?.map((notification) => ({
        ...notification,
        type: determineNotificationType(notification),
      })) || [];

    // Stream notifications (transformed)
    const transformedStreamNotifications: StreamNotificationExtended[] =
      streamNotifications?.map(transformStreamNotification) || [];

    // Combine all notifications
    const allNotifications: CombinedNotification[] = [
      ...regularNotifications,
      ...transformedStreamNotifications,
    ];

    // Sort by creation date (newest first)
    allNotifications.sort((a, b) => {
      const dateA = new Date(a.createdAt);
      const dateB = new Date(b.createdAt);
      return dateB.getTime() - dateA.getTime();
    });

    // Filter by type based on active tab
    return allNotifications.filter((notification) =>
      activeTab === "activities"
        ? notification.type === "activity"
        : notification.type === "announcement",
    );
  }, [data, streamNotifications, activeTab]);

  const renderNotification = ({ item }: { item: CombinedNotification }) => {
    return <NotificationItem item={item} />;
  };

  const renderEmptyState = () => {
    if (isLoading || streamLoading) {
      return (
        <View style={styles.emptyContainer}>
          <ActivityIndicator size="large" color="#fff" />
          <Text style={styles.emptyMessage}>Loading notifications...</Text>
        </View>
      );
    }

    if (isError) {
      return (
        <View style={styles.emptyContainer}>
          <MaterialIcons name="error-outline" size={48} color="#838A94" />
          <Text style={styles.emptyTitle}>Something went wrong</Text>
          <Text style={styles.emptyMessage}>
            We couldn't load your notifications
          </Text>
        </View>
      );
    }

    return (
      <View style={styles.emptyContainer}>
        <Bell size={48} color="#838A94" />
        <Text style={styles.emptyTitle}>No News Yet</Text>
        <Text style={styles.emptyMessage}>
          We'll notify you as soon as we hear anything
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.inner}>
        <Text style={styles.title}>Notifications</Text>

        {/* Tabs */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[styles.tab, activeTab === "activities" && styles.activeTab]}
            onPress={() => handleTabPress("activities")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "activities" && styles.activeTabText,
              ]}
            >
              Activities
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === "announcements" && styles.activeTab,
            ]}
            onPress={() => handleTabPress("announcements")}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === "announcements" && styles.activeTabText,
              ]}
            >
              Announcements
            </Text>
          </TouchableOpacity>
        </View>

        {filteredNotifications.length > 0 ? (
          <FlatList
            data={filteredNotifications}
            renderItem={renderNotification}
            keyExtractor={(item) => {
              // Handle both string and number IDs
              if (typeof item.id === "string") {
                return item.id;
              }
              return item.id.toString();
            }}
            contentContainerStyle={styles.listContent}
            showsVerticalScrollIndicator={false}
            removeClippedSubviews={true}
            maxToRenderPerBatch={10}
            windowSize={10}
            refreshControl={
              <RefreshControl
                refreshing={isRefreshing}
                onRefresh={async () => {
                  setIsRefreshing(true);
                  try {
                    await Promise.all([refetch(), fetchStreamNotifications()]);
                  } catch (error) {
                    console.error("Failed to refresh notifications:", error);
                    Alert.alert(
                      "Error",
                      "Failed to refresh notifications. Please try again.",
                    );
                  } finally {
                    setIsRefreshing(false);
                  }
                }}
                colors={["#007AFF"]}
                tintColor="#007AFF"
                title="Refreshing..."
                titleColor="#9A9A9A"
                progressBackgroundColor="#1A1A1A"
              />
            }
            getItemLayout={(data, index) => ({
              length: 100, // Approximate height of each item
              offset: 100 * index,
              index,
            })}
          />
        ) : (
          renderEmptyState()
        )}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000",
  },
  inner: {
    padding: 16,
    flex: 1,
  },
  title: {
    fontSize: 20,
    fontWeight: "800",
    color: "#fff",
    marginBottom: 24,
  },
  tabContainer: {
    flexDirection: "row",
    gap: 8,
    marginBottom: 16,
  },
  tab: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 12,
    backgroundColor: "#585858",
  },
  activeTab: {
    backgroundColor: "#fff",
  },
  tabText: {
    fontSize: 12,
    fontWeight: "500",
    color: "#B0B0B0",
    lineHeight: 16,
  },
  activeTabText: {
    color: "#242424",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: "600",
    color: "#fff",
    textAlign: "center",
  },
  emptyMessage: {
    fontSize: 16,
    color: "#9A9A9A",
    textAlign: "center",
  },
  listContent: {
    paddingBottom: 16,
  },
  notificationItem: {
    backgroundColor: "#1A1A1A",
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  notificationItemDisabled: {
    opacity: 0.7,
  },
  notificationHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  avatarPlaceholder: {
    backgroundColor: "#585858",
    justifyContent: "center",
    alignItems: "center",
  },
  notificationContent: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#fff",
    marginBottom: 4,
  },
  notificationMessage: {
    fontSize: 14,
    color: "#9A9A9A",
    marginBottom: 8,
  },
  notificationTime: {
    fontSize: 12,
    color: "#585858",
  },
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: "#007AFF",
    marginLeft: 8,
  },
});

export default NotificationsScreen;
